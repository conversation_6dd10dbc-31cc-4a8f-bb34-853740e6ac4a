Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.21f1c1 (08fa194de70f) revision 588313'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 32717 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\2021.3.21f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
G:/GAME_ZD/Brotato_LegionSurvivors
-logFile
Logs/AssetImportWorker2.log
-srvPort
5866
Successfully changed project path to: G:/GAME_ZD/Brotato_LegionSurvivors
G:/GAME_ZD/Brotato_LegionSurvivors
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24324] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4245145433 [EditorId] 4245145433 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [24324] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4245145433 [EditorId] 4245145433 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 182.72 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.21f1c1 (08fa194de70f)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/GAME_ZD/Brotato_LegionSurvivors/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56960
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004533 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 264 ms
Refreshing native plugins compatible for Editor in 169.23 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.086 seconds
Domain Reload Profiling:
	ReloadAssembly (1087ms)
		BeginReloadAssembly (91ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (2ms)
		EndReloadAssembly (880ms)
			LoadAssemblies (87ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (140ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (28ms)
			SetupLoadedEditorAssemblies (643ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (333ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (169ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (96ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.012064 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 190.39 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.454 seconds
Domain Reload Profiling:
	ReloadAssembly (1455ms)
		BeginReloadAssembly (126ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (1209ms)
			LoadAssemblies (95ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (229ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (88ms)
			SetupLoadedEditorAssemblies (728ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (190ms)
				BeforeProcessingInitializeOnLoad (84ms)
				ProcessInitializeOnLoadAttributes (373ms)
				ProcessInitializeOnLoadMethodAttributes (52ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 4.68 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 130 unused Assets / (134.3 KB). Loaded Objects now: 3664.
Memory consumption went from 152.4 MB to 152.2 MB.
Total: 4.828200 ms (FindLiveObjects: 0.513600 ms CreateObjectMapping: 0.118900 ms MarkObjects: 3.987300 ms  DeleteObjects: 0.207000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 173579.395527 seconds.
  path: Assets/ScenesPortrait_dungeon/Prefab/DraggableItemFollowerUIPrefab.prefab
  artifactKey: Guid(e0bca8d0f17a0c94ea611de665250c9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Prefab/DraggableItemFollowerUIPrefab.prefab using Guid(e0bca8d0f17a0c94ea611de665250c9e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '343c1a2705d016bd6666a5563c5edc50') in 0.063568 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Prefabs/Items.prefab
  artifactKey: Guid(3bfbd543323063b49b82b002cb8cf91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Items.prefab using Guid(3bfbd543323063b49b82b002cb8cf91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0b7c421093b20deca3e4e4920555da7e') in 0.153718 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Resources/Effects/Prop/SpicySauce/ItemExplodingEffect.asset
  artifactKey: Guid(afc2ecdfd332f7948b00b14b5ea097b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Effects/Prop/SpicySauce/ItemExplodingEffect.asset using Guid(afc2ecdfd332f7948b00b14b5ea097b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:109)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:109)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:109)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: '83db9dbc712686270162b8f9133cc0a7') in 0.926299 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Resources/Singletons/ItemService.asset
  artifactKey: Guid(5436a2974d35035468b11c8953624496) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Singletons/ItemService.asset using Guid(5436a2974d35035468b11c8953624496) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: '4b1a16355a44c10db64dc66f135faeb6') in 0.804622 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Resources/Effects/Prop/Landmines/ItemExplodingEffect.asset
  artifactKey: Guid(87e96b200ea3e1640bb5d009c2fd9a35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Effects/Prop/Landmines/ItemExplodingEffect.asset using Guid(87e96b200ea3e1640bb5d009c2fd9a35) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: 'a38c8d233ad2bb65ba366e8ac20e1d04') in 0.738034 seconds 
========================================================================
Received Import Request.
  Time since last request: 20.204808 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_bg01.png
  artifactKey: Guid(b8c3cb2dea7cdf84cabcae206a3b411b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_bg01.png using Guid(b8c3cb2dea7cdf84cabcae206a3b411b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cfa6ea3796cc6109dabf6b9b67f51038') in 0.108912 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k010.png
  artifactKey: Guid(e8393e51333db1649b8c6ce35cfe5696) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k010.png using Guid(e8393e51333db1649b8c6ce35cfe5696) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fa4893dd95d734203e552acaded00244') in 0.015481 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k04.png
  artifactKey: Guid(02d7b84393229394f981d8a51c817acd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k04.png using Guid(02d7b84393229394f981d8a51c817acd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b514a7b69e20bf6936a97404dc3cce01') in 0.014630 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k03.png
  artifactKey: Guid(5ba8582708baf5e4aaa8d3f51aded313) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k03.png using Guid(5ba8582708baf5e4aaa8d3f51aded313) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '53492fa9254d346c6d75d5d712ca89f8') in 0.011421 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k011.png
  artifactKey: Guid(56f0e7b9925202f4fa62a6c2d90fc84f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k011.png using Guid(56f0e7b9925202f4fa62a6c2d90fc84f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bef84f8112dbc35fc8bc74831cba52df') in 0.010618 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k09.png
  artifactKey: Guid(69f223f94ec85ea4994f5fe22c33730d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/图鉴/UI_tj_k09.png using Guid(69f223f94ec85ea4994f5fe22c33730d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '297e7064608d4c8d6d624eee8a5d73b2') in 0.013065 seconds 
========================================================================
Received Import Request.
  Time since last request: 22.378173 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_bgk02.png
  artifactKey: Guid(00de72442f9b60842b384c898bf8c623) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_bgk02.png using Guid(00de72442f9b60842b384c898bf8c623) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '29e3c94b8627ba7e6d69ee8bea684bac') in 0.033529 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_hs_bg.png
  artifactKey: Guid(5726a04187966a64780a048d5a2c8518) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_hs_bg.png using Guid(5726a04187966a64780a048d5a2c8518) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'de23832b4e55fde62641fda8c07ce628') in 0.013368 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_hs.png
  artifactKey: Guid(907b68de2f6b28a46b519f171823c7a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_hs.png using Guid(907b68de2f6b28a46b519f171823c7a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ae95ddb5e79f3c94df173377892fcb18') in 0.011130 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_wq_bg_01.png
  artifactKey: Guid(c90fedc379a7b5743af965dd45d3bee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_wq_bg_01.png using Guid(c90fedc379a7b5743af965dd45d3bee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '30113b029bd23e1354773f3d3beb1773') in 0.015689 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_zz_an.png
  artifactKey: Guid(9906342496f5bf842a5122383d41abd4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_zz_an.png using Guid(9906342496f5bf842a5122383d41abd4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'df492c52f1510a91f23143a34fa711ab') in 0.013018 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_khc.png
  artifactKey: Guid(71c8aeff211317740820b26ca8073f28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_khc.png using Guid(71c8aeff211317740820b26ca8073f28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6a67777a4267c427384070aec12f90c9') in 0.013700 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_wq_bg_02.png
  artifactKey: Guid(596a0606e80cef14dbaad10124bd6c65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_sp_wq_bg_02.png using Guid(596a0606e80cef14dbaad10124bd6c65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '639997831a8f0486c9152eebb54a7d77') in 0.015967 seconds 
========================================================================
Received Import Request.
  Time since last request: 7.237681 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_zztb.png
  artifactKey: Guid(f051c5c4ee6251a4292c9c335ec4d383) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/UI_zztb.png using Guid(f051c5c4ee6251a4292c9c335ec4d383) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7bdca9a425bb8f6e1b90880ad4e35ff9') in 0.013969 seconds 
========================================================================
Received Import Request.
  Time since last request: 13.635130 seconds.
  path: Assets/GameResources/Dungeon/Singletons/ItemService.asset
  artifactKey: Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources/Dungeon/Singletons/ItemService.asset using Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: 'fb3b92fdc995666e136b63717b020a8b') in 0.688771 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015755 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.28 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.158 seconds
Domain Reload Profiling:
	ReloadAssembly (1159ms)
		BeginReloadAssembly (149ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (38ms)
		EndReloadAssembly (893ms)
			LoadAssemblies (96ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (214ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (391ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (64ms)
				ProcessInitializeOnLoadAttributes (251ms)
				ProcessInitializeOnLoadMethodAttributes (46ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (84ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 20.05 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 106 unused Assets / (128.2 KB). Loaded Objects now: 3720.
Memory consumption went from 153.2 MB to 153.1 MB.
Total: 4.909700 ms (FindLiveObjects: 0.498100 ms CreateObjectMapping: 0.117600 ms MarkObjects: 4.142700 ms  DeleteObjects: 0.149500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 220.651554 seconds.
  path: Assets/GameResources/Dungeon/Singletons/ItemService.asset
  artifactKey: Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources/Dungeon/Singletons/ItemService.asset using Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: 'ec888e66f9c5e1a2d274e52b9060e433') in 0.657100 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011226 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.35 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.086 seconds
Domain Reload Profiling:
	ReloadAssembly (1087ms)
		BeginReloadAssembly (141ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (835ms)
			LoadAssemblies (89ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (200ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (45ms)
			SetupLoadedEditorAssemblies (372ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (61ms)
				ProcessInitializeOnLoadAttributes (239ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (78ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.63 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3735.
Memory consumption went from 153.4 MB to 153.3 MB.
Total: 5.197500 ms (FindLiveObjects: 0.600700 ms CreateObjectMapping: 0.114100 ms MarkObjects: 4.389300 ms  DeleteObjects: 0.091000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 5.060104 seconds.
  path: Assets/GameResources/Dungeon/Singletons/ItemService.asset
  artifactKey: Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources/Dungeon/Singletons/ItemService.asset using Guid(0bce07fb20ed69c4a99a5fb6f9d6397e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: '7e807cca67616e3ddfca5f0d6517d29e') in 0.751330 seconds 
========================================================================
Received Import Request.
  Time since last request: 24.940311 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/商店/x.png
  artifactKey: Guid(54a0dc15a4cab1945b2fc1b6e735e527) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/商店/x.png using Guid(54a0dc15a4cab1945b2fc1b6e735e527) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e22dc904d8769b05becdea0de2d19fbc') in 0.045737 seconds 
========================================================================
Received Import Request.
  Time since last request: 237.745235 seconds.
  path: Assets/ScenesPortrait_dungeon/UIMenus/MenuCatalog.prefab
  artifactKey: Guid(1df7dd29ad4850747b1d2a39e5c6c01f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/UIMenus/MenuCatalog.prefab using Guid(1df7dd29ad4850747b1d2a39e5c6c01f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '690328d7f30eb614e0b814b1226d7e0d') in 0.027120 seconds 
========================================================================
Received Import Request.
  Time since last request: 94.194267 seconds.
  path: Assets/ScenesPortrait_dungeon/UIMenus/MenuCatalog.prefab
  artifactKey: Guid(1df7dd29ad4850747b1d2a39e5c6c01f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/UIMenus/MenuCatalog.prefab using Guid(1df7dd29ad4850747b1d2a39e5c6c01f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ee810afe68a45ce2a8f3898c92c43b6c') in 0.012292 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013104 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.28 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.116 seconds
Domain Reload Profiling:
	ReloadAssembly (1117ms)
		BeginReloadAssembly (135ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (878ms)
			LoadAssemblies (88ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (216ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (380ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (67ms)
				ProcessInitializeOnLoadAttributes (236ms)
				ProcessInitializeOnLoadMethodAttributes (47ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (77ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.64 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 107 unused Assets / (102.2 KB). Loaded Objects now: 3754.
Memory consumption went from 153.5 MB to 153.4 MB.
Total: 5.159400 ms (FindLiveObjects: 0.504800 ms CreateObjectMapping: 0.120600 ms MarkObjects: 4.401400 ms  DeleteObjects: 0.131000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 6054.274927 seconds.
  path: Assets/Scenes/UIMenus/UIGamePlayerUI.prefab
  artifactKey: Guid(ae43eff9d7e7ce24b8b9cb9db8b53b24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/UIMenus/UIGamePlayerUI.prefab using Guid(ae43eff9d7e7ce24b8b9cb9db8b53b24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fbf70e8be9f3aae2f627b9392aedf6b0') in 0.052525 seconds 
========================================================================
Received Import Request.
  Time since last request: 52.946518 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_cdl_an01.png
  artifactKey: Guid(9d5d9ca7ac541a5479791e120ff6753b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_cdl_an01.png using Guid(9d5d9ca7ac541a5479791e120ff6753b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '493fd019f7e44496303afba1252f07a0') in 0.049313 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_coin.png
  artifactKey: Guid(ae8d6484333fc2346a0d11d6e1a58d47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_coin.png using Guid(ae8d6484333fc2346a0d11d6e1a58d47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b656649dc60110c159a14a3c702c56fd') in 0.011943 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_Soul.png
  artifactKey: Guid(e7a306eb62cec534dab77e3286ca37d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_Soul.png using Guid(e7a306eb62cec534dab77e3286ca37d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '348e266679393ad951b155af5a8343cb') in 0.010339 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_dt_01.png
  artifactKey: Guid(bc60d6298741f6a409f59e0585aaa190) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_dt_01.png using Guid(bc60d6298741f6a409f59e0585aaa190) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4f68dd6d98081b860145208aa04d7e11') in 0.047778 seconds 
========================================================================
Received Import Request.
  Time since last request: 8.332509 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_men_bg.png
  artifactKey: Guid(f6c78aa333687df4681281d498496177) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/大厅_战斗/UI_men_bg.png using Guid(f6c78aa333687df4681281d498496177) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '26a875a50488da798602a88515d50cab') in 0.012238 seconds 
========================================================================
Received Import Request.
  Time since last request: 198.344413 seconds.
  path: Assets/I2/Localization/Examples/Common/COMIC.TTF
  artifactKey: Guid(769d4a2288b99004184236f5fc4469c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/I2/Localization/Examples/Common/COMIC.TTF using Guid(769d4a2288b99004184236f5fc4469c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '525b34be25a75161b2ed84e60e185c90') in 0.051383 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Font/raw/Mini.ttf
  artifactKey: Guid(83c1b3445be538240ab83da7c8d3c185) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Font/raw/Mini.ttf using Guid(83c1b3445be538240ab83da7c8d3c185) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'caaac44fc0eb10bff7c445150f144dee') in 0.016680 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011007 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.18 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.121 seconds
Domain Reload Profiling:
	ReloadAssembly (1122ms)
		BeginReloadAssembly (149ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (871ms)
			LoadAssemblies (99ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (213ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (378ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (61ms)
				ProcessInitializeOnLoadAttributes (244ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (82ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.63 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3818.
Memory consumption went from 153.9 MB to 153.8 MB.
Total: 5.204700 ms (FindLiveObjects: 0.501100 ms CreateObjectMapping: 0.114700 ms MarkObjects: 4.461400 ms  DeleteObjects: 0.125200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011969 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.42 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.219 seconds
Domain Reload Profiling:
	ReloadAssembly (1219ms)
		BeginReloadAssembly (150ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (958ms)
			LoadAssemblies (103ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (230ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (54ms)
			SetupLoadedEditorAssemblies (428ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (277ms)
				ProcessInitializeOnLoadMethodAttributes (48ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (80ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.94 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3833.
Memory consumption went from 153.9 MB to 153.8 MB.
Total: 5.666600 ms (FindLiveObjects: 0.574300 ms CreateObjectMapping: 0.122300 ms MarkObjects: 4.823900 ms  DeleteObjects: 0.144400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016857 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.60 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.201 seconds
Domain Reload Profiling:
	ReloadAssembly (1201ms)
		BeginReloadAssembly (158ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (922ms)
			LoadAssemblies (112ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (215ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (61ms)
			SetupLoadedEditorAssemblies (403ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (67ms)
				ProcessInitializeOnLoadAttributes (257ms)
				ProcessInitializeOnLoadMethodAttributes (47ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (83ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 20.69 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.6 KB). Loaded Objects now: 3848.
Memory consumption went from 153.9 MB to 153.8 MB.
Total: 6.021000 ms (FindLiveObjects: 0.595500 ms CreateObjectMapping: 0.216700 ms MarkObjects: 5.090800 ms  DeleteObjects: 0.116100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014691 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.86 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.104 seconds
Domain Reload Profiling:
	ReloadAssembly (1105ms)
		BeginReloadAssembly (137ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (31ms)
		EndReloadAssembly (861ms)
			LoadAssemblies (92ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (205ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (387ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (64ms)
				ProcessInitializeOnLoadAttributes (249ms)
				ProcessInitializeOnLoadMethodAttributes (46ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (81ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 15.89 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.6 KB). Loaded Objects now: 3863.
Memory consumption went from 153.9 MB to 153.8 MB.
Total: 5.291900 ms (FindLiveObjects: 0.465800 ms CreateObjectMapping: 0.114900 ms MarkObjects: 4.593200 ms  DeleteObjects: 0.116300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 602.865826 seconds.
  path: Assets/RawResources/ParticleImage/Highlight 1.prefab
  artifactKey: Guid(bcc33a54a47748845b8db64b0e49c6f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/ParticleImage/Highlight 1.prefab using Guid(bcc33a54a47748845b8db64b0e49c6f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c6004712e1899de7c6921703b9632fc4') in 0.034281 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/RawResources/ParticleImage/UniformEmitter 1.prefab
  artifactKey: Guid(abff38c38fe4f8f4cbb786a12939b8c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/ParticleImage/UniformEmitter 1.prefab using Guid(abff38c38fe4f8f4cbb786a12939b8c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4f6a8f5767eefd03d0c52aa6769b39db') in 0.005259 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014821 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.93 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.022 seconds
Domain Reload Profiling:
	ReloadAssembly (1022ms)
		BeginReloadAssembly (136ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (31ms)
		EndReloadAssembly (773ms)
			LoadAssemblies (92ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (201ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (43ms)
			SetupLoadedEditorAssemblies (332ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (55ms)
				ProcessInitializeOnLoadAttributes (216ms)
				ProcessInitializeOnLoadMethodAttributes (37ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (66ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 19.49 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3878.
Memory consumption went from 154.0 MB to 153.9 MB.
Total: 5.625700 ms (FindLiveObjects: 0.438900 ms CreateObjectMapping: 0.135000 ms MarkObjects: 4.971400 ms  DeleteObjects: 0.079100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 201.395319 seconds.
  path: Assets/RawResources/Emoji/emoji_tex.png
  artifactKey: Guid(1c025cc0bd7e71f4fb710ae672cd2364) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/Emoji/emoji_tex.png using Guid(1c025cc0bd7e71f4fb710ae672cd2364) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9270be98f245556d0c8f9d93d51650ff') in 0.090539 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.542172 seconds.
  path: Assets/RawResources/Map/Map_Basic+_+++(2).png
  artifactKey: Guid(08a709aa5a23a4f499ca099f9c8762aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/Map/Map_Basic+_+++(2).png using Guid(08a709aa5a23a4f499ca099f9c8762aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '09dae735f7cd63c04d8dbe7de7bab965') in 0.359229 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawResources/Map/Map_Basic.png
  artifactKey: Guid(f5d1dc625f6e3a04c87c939de3677a59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/Map/Map_Basic.png using Guid(f5d1dc625f6e3a04c87c939de3677a59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '86dee28d352c8b80fc32acfccdbced05') in 0.228375 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/RawResources/Map/Map_Basic+_++.png
  artifactKey: Guid(2f1a826ed380c1743929e0eaf950709e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/Map/Map_Basic+_++.png using Guid(2f1a826ed380c1743929e0eaf950709e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0e1cf1eb787600dfae80b564013ff7eb') in 0.243354 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.778711 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_01.png
  artifactKey: Guid(7ef010481caf36d40a871fcfe715d824) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_01.png using Guid(7ef010481caf36d40a871fcfe715d824) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '57ee036c5948586d730677e5dbba4c6f') in 0.019967 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_06.png
  artifactKey: Guid(3714fce7e27748a42877ed325eb69ea4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_06.png using Guid(3714fce7e27748a42877ed325eb69ea4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1bcbe07af341da0619823d4667fad965') in 0.020872 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_020.png
  artifactKey: Guid(59c1bdbbd0fc072469a782698ade0d7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_020.png using Guid(59c1bdbbd0fc072469a782698ade0d7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2f2c0b78d5b5aa345d9b9a1f9cc3986e') in 0.013325 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_02.png
  artifactKey: Guid(14d7dccf32ef86a4dab3813758ce31ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_02.png using Guid(14d7dccf32ef86a4dab3813758ce31ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd031757b1b53a2b0a3f5e0818c6cc332') in 0.015085 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_013.png
  artifactKey: Guid(1c4377c10a54c4543b6e70b967a8d770) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_013.png using Guid(1c4377c10a54c4543b6e70b967a8d770) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '96441f70392d91fd2f496633ef3fe421') in 0.013322 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_011.png
  artifactKey: Guid(12c8a9b9084eb6d48a331001c6b79709) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_011.png using Guid(12c8a9b9084eb6d48a331001c6b79709) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1ccdaa2371ecafe8b56aa6a5781e095e') in 0.014537 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_010.png
  artifactKey: Guid(c469d9d6e410e88428c7b1fa1986a1c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_010.png using Guid(c469d9d6e410e88428c7b1fa1986a1c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5c4c92b39a7d6123f9beb15fb2d75649') in 0.013135 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_014.png
  artifactKey: Guid(1b8b23cc4ba5a774ea07d24500fa1cd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_014.png using Guid(1b8b23cc4ba5a774ea07d24500fa1cd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4fb87c250c29eb9d9a37a9ae16c1372d') in 0.012845 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_03.png
  artifactKey: Guid(7f0841032012e9c419f3dc3dc5fe86f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_03.png using Guid(7f0841032012e9c419f3dc3dc5fe86f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '229d155ee568e324ead7b9a08a166374') in 0.013853 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_016.png
  artifactKey: Guid(f19758eb321227e49aaa5c9089816497) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RawResources/0403_UI切图效果图更新/切图/遗物/UI_yw_016.png using Guid(f19758eb321227e49aaa5c9089816497) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '72c1b4c4096644573915127f7f23df44') in 0.011211 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013399 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.45 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.148 seconds
Domain Reload Profiling:
	ReloadAssembly (1149ms)
		BeginReloadAssembly (141ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (898ms)
			LoadAssemblies (94ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (203ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (409ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (261ms)
				ProcessInitializeOnLoadMethodAttributes (47ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (84ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.26 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3214 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (100.7 KB). Loaded Objects now: 3902.
Memory consumption went from 154.0 MB to 153.9 MB.
Total: 5.865400 ms (FindLiveObjects: 0.667400 ms CreateObjectMapping: 0.261000 ms MarkObjects: 4.818800 ms  DeleteObjects: 0.115600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0