using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UISelectButton : MonoBehaviour
{
    public Image bg;
    public Text text;
    public Image Img;
    public Color[] colorBg;
    public Color[] colorText;
    public Image[] images;
    public Sprite[] sprites;
    public void SetState(int select)
    {
        if(colorBg.Length > select)
        {
            bg.color = colorBg[select];
        }
        if (colorText.Length > select)
        {
            text.color = colorText[select];
        }
        for (int i = 0; i < images.Length; i++)
        {
            images[i].sprite = sprites[images.Length * i + select];
        }
    }
    public void SetColorState(string title, int select)
    {
        text.text = title;
        SetState(select);
    }
    public void SetImgVisible(bool Visible)
    {
        Img.gameObject.SetActive(Visible);
    }
    public void SetInteractable(bool interactable)
    {
        GetComponent<Button>().interactable = interactable;
    }
}
